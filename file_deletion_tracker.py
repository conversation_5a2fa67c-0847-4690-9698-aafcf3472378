#!/usr/bin/env python3
"""
File Deletion Tracker - NTFS Journal Analysis Tool
Analyzes NTFS Journal ($UsnJrnl) to find deleted files and folders
Runs from administrator workstation to monitor file servers remotely
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import os
import sys
import threading
import csv
import json
from pathlib import Path
import ctypes
from ctypes import wintypes
import struct

# Windows API imports - will install these packages if needed
try:
    import win32api
    import win32file
    import win32con
    import win32security
    import win32net
    import win32netcon
except ImportError:
    print("Required Windows API packages not found.")
    print("Please install with: pip install pywin32")
    sys.exit(1)

class FileDeleteionTracker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("File Deletion Tracker - NTFS Journal Analysis")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)

        # Variables
        self.selected_drive = tk.StringVar()
        self.start_date = tk.StringVar()
        self.end_date = tk.StringVar()
        self.start_time = tk.StringVar(value="00:00:00")
        self.end_time = tk.StringVar(value="23:59:59")

        # Results storage
        self.deletion_records = []

        # Initialize GUI
        self.create_widgets()
        self.populate_drives()

        # Set default dates (last 30 days)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        self.start_date.set(start_date.strftime("%Y-%m-%d"))
        self.end_date.set(end_date.strftime("%Y-%m-%d"))

    def create_widgets(self):
        """Create the main GUI interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="File Deletion Tracker",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # Drive selection
        ttk.Label(main_frame, text="Select Drive:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.drive_combo = ttk.Combobox(main_frame, textvariable=self.selected_drive,
                                       width=50, state="readonly")
        self.drive_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Refresh drives button
        refresh_btn = ttk.Button(main_frame, text="Refresh", command=self.populate_drives)
        refresh_btn.grid(row=1, column=2, pady=5, padx=(10, 0))

        # Date/Time selection frame
        datetime_frame = ttk.LabelFrame(main_frame, text="Date/Time Range", padding="10")
        datetime_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        datetime_frame.columnconfigure(1, weight=1)
        datetime_frame.columnconfigure(3, weight=1)

        # Start date/time
        ttk.Label(datetime_frame, text="Start Date:").grid(row=0, column=0, sticky=tk.W)
        start_date_entry = ttk.Entry(datetime_frame, textvariable=self.start_date, width=12)
        start_date_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 20))

        ttk.Label(datetime_frame, text="Time:").grid(row=0, column=2, sticky=tk.W)
        start_time_entry = ttk.Entry(datetime_frame, textvariable=self.start_time, width=10)
        start_time_entry.grid(row=0, column=3, sticky=tk.W, padx=(10, 0))

        # End date/time
        ttk.Label(datetime_frame, text="End Date:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        end_date_entry = ttk.Entry(datetime_frame, textvariable=self.end_date, width=12)
        end_date_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 20), pady=(10, 0))

        ttk.Label(datetime_frame, text="Time:").grid(row=1, column=2, sticky=tk.W, pady=(10, 0))
        end_time_entry = ttk.Entry(datetime_frame, textvariable=self.end_time, width=10)
        end_time_entry.grid(row=1, column=3, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        # Format help
        ttk.Label(datetime_frame, text="Date format: YYYY-MM-DD, Time format: HH:MM:SS",
                 font=("Arial", 8)).grid(row=2, column=0, columnspan=4, pady=(10, 0))

        # Control buttons frame
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=3, pady=20)

        # Analyze button
        self.analyze_btn = ttk.Button(control_frame, text="Analyze Deletions",
                                     command=self.start_analysis)
        self.analyze_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Export button
        self.export_btn = ttk.Button(control_frame, text="Export Results",
                                    command=self.export_results, state="disabled")
        self.export_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Clear button
        clear_btn = ttk.Button(control_frame, text="Clear Results", command=self.clear_results)
        clear_btn.pack(side=tk.LEFT)

        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Results frame
        results_frame = ttk.LabelFrame(main_frame, text="Deletion Records", padding="5")
        results_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # Results treeview
        columns = ("DateTime", "User", "Type", "Path", "FileName")
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=15)

        # Configure columns
        self.results_tree.heading("DateTime", text="Date/Time")
        self.results_tree.heading("User", text="User")
        self.results_tree.heading("Type", text="Type")
        self.results_tree.heading("Path", text="Path")
        self.results_tree.heading("FileName", text="File/Folder Name")

        self.results_tree.column("DateTime", width=150)
        self.results_tree.column("User", width=120)
        self.results_tree.column("Type", width=80)
        self.results_tree.column("Path", width=300)
        self.results_tree.column("FileName", width=200)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid treeview and scrollbars
        self.results_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

    def populate_drives(self):
        """Populate the drive dropdown with available drives"""
        drives = []

        # Get all logical drives (including mapped network drives)
        drive_bitmask = win32api.GetLogicalDrives()
        for i, letter in enumerate("ABCDEFGHIJKLMNOPQRSTUVWXYZ"):
            if drive_bitmask & (1 << i):
                drive_path = f"{letter}:\\"
                try:
                    drive_type = win32file.GetDriveType(drive_path)

                    # Include fixed drives and network drives
                    if drive_type == win32con.DRIVE_FIXED:
                        try:
                            volume_info = win32api.GetVolumeInformation(drive_path)
                            volume_label = volume_info[0] if volume_info[0] else "Local Disk"
                            drives.append(f"{letter}: ({volume_label}) - Local Drive")
                        except:
                            drives.append(f"{letter}: (Unknown) - Local Drive")

                    elif drive_type == win32con.DRIVE_REMOTE:
                        try:
                            # Get network path for mapped drives
                            network_path = win32api.WNetGetConnection(f"{letter}:")
                            volume_info = win32api.GetVolumeInformation(drive_path)
                            volume_label = volume_info[0] if volume_info[0] else "Network Drive"
                            drives.append(f"{letter}: ({volume_label}) -> {network_path}")
                        except:
                            drives.append(f"{letter}: (Network Drive) -> Unknown Path")

                    elif drive_type == win32con.DRIVE_REMOVABLE:
                        try:
                            volume_info = win32api.GetVolumeInformation(drive_path)
                            volume_label = volume_info[0] if volume_info[0] else "Removable Drive"
                            drives.append(f"{letter}: ({volume_label}) - Removable Drive")
                        except:
                            drives.append(f"{letter}: (Removable Drive)")

                except Exception as e:
                    # Drive might not be accessible
                    continue

        # Also try to get network connections directly
        try:
            import subprocess
            result = subprocess.run(['net', 'use'], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if ':' in line and '\\\\' in line:
                        parts = line.split()
                        if len(parts) >= 3:
                            local_drive = parts[1] if len(parts[1]) == 2 and parts[1].endswith(':') else None
                            network_path = None
                            for part in parts:
                                if part.startswith('\\\\'):
                                    network_path = part
                                    break

                            if local_drive and network_path:
                                # Check if we already have this drive
                                existing = any(drive.startswith(local_drive) for drive in drives)
                                if not existing:
                                    drives.append(f"{local_drive} (Mapped Network Drive) -> {network_path}")
        except:
            pass

        # Update combobox
        self.drive_combo['values'] = drives
        if drives:
            self.drive_combo.set(drives[0])

        self.status_var.set(f"Found {len(drives)} drives")

    def start_analysis(self):
        """Start the deletion analysis in a separate thread"""
        if not self.selected_drive.get():
            messagebox.showerror("Error", "Please select a drive to analyze")
            return

        # Validate date/time inputs
        try:
            start_datetime = datetime.strptime(f"{self.start_date.get()} {self.start_time.get()}",
                                             "%Y-%m-%d %H:%M:%S")
            end_datetime = datetime.strptime(f"{self.end_date.get()} {self.end_time.get()}",
                                           "%Y-%m-%d %H:%M:%S")

            if start_datetime >= end_datetime:
                messagebox.showerror("Error", "Start date/time must be before end date/time")
                return

        except ValueError as e:
            messagebox.showerror("Error", f"Invalid date/time format: {e}")
            return

        # Disable analyze button and start progress
        self.analyze_btn.config(state="disabled")
        self.progress.start()
        self.status_var.set("Analyzing NTFS Journal...")

        # Start analysis in separate thread
        analysis_thread = threading.Thread(target=self.analyze_deletions,
                                          args=(start_datetime, end_datetime))
        analysis_thread.daemon = True
        analysis_thread.start()

    def analyze_deletions(self, start_datetime, end_datetime):
        """Analyze NTFS Journal for deletions in the specified time range"""
        try:
            # Extract drive letter from selection
            drive_selection = self.selected_drive.get()
            drive_letter = drive_selection.split(':')[0]

            # Get NTFS Journal data
            deletions = self.read_ntfs_journal(drive_letter, start_datetime, end_datetime)

            # Update GUI in main thread
            self.root.after(0, self.display_results, deletions)

        except Exception as e:
            self.root.after(0, self.analysis_error, str(e))

    def read_ntfs_journal(self, drive_letter, start_datetime, end_datetime):
        """Read and parse NTFS Journal for deletion records"""
        deletions = []

        try:
            # Open volume handle
            volume_path = f"\\\\.\\{drive_letter}:"
            volume_handle = win32file.CreateFile(
                volume_path,
                win32con.GENERIC_READ,
                win32con.FILE_SHARE_READ | win32con.FILE_SHARE_WRITE,
                None,
                win32con.OPEN_EXISTING,
                0,
                None
            )

            # Get USN Journal data
            # FSCTL_QUERY_USN_JOURNAL = 0x000900f4
            FSCTL_QUERY_USN_JOURNAL = 0x000900f4

            try:
                # Query the USN Journal
                journal_data = win32file.DeviceIoControl(
                    volume_handle,
                    FSCTL_QUERY_USN_JOURNAL,
                    None,
                    1024,
                    None
                )

                if journal_data:
                    # Parse journal data (simplified version)
                    # In a full implementation, we would parse the actual USN records
                    # For now, let's try to get some basic information

                    # Try to read some USN records
                    FSCTL_READ_USN_JOURNAL = 0x000900bb

                    # Create READ_USN_JOURNAL_DATA structure
                    read_data = struct.pack('<QQI', 0, 0xFFFFFFFFFFFFFFFF, 0)

                    try:
                        usn_data = win32file.DeviceIoControl(
                            volume_handle,
                            FSCTL_READ_USN_JOURNAL,
                            read_data,
                            65536,
                            None
                        )

                        if usn_data and len(usn_data) > 8:
                            # Parse USN records
                            deletions = self.parse_usn_records(usn_data, start_datetime, end_datetime, drive_letter)
                        else:
                            # No USN data available
                            self.root.after(0, lambda: messagebox.showinfo(
                                "No Data",
                                f"No USN Journal data found for drive {drive_letter}:\n"
                                "This could mean:\n"
                                "• The drive doesn't support USN Journal\n"
                                "• No recent file operations occurred\n"
                                "• Journal data has been overwritten"
                            ))

                    except Exception as e:
                        # Try alternative approach - check recent file operations via file system
                        deletions = self.check_recent_file_operations(drive_letter, start_datetime, end_datetime)

                else:
                    self.root.after(0, lambda: messagebox.showinfo(
                        "No Journal",
                        f"USN Journal not available for drive {drive_letter}:\n"
                        "This drive may not support change journaling."
                    ))

            except Exception as e:
                # Fallback: Try to analyze using file system events or other methods
                deletions = self.check_recent_file_operations(drive_letter, start_datetime, end_datetime)

            win32file.CloseHandle(volume_handle)

        except Exception as e:
            raise Exception(f"Failed to access drive {drive_letter}: {e}")

        return deletions

    def parse_usn_records(self, usn_data, start_datetime, end_datetime, drive_letter):
        """Parse USN records for deletion events"""
        deletions = []

        try:
            # Skip the first 8 bytes (next USN)
            offset = 8

            while offset < len(usn_data):
                if offset + 60 > len(usn_data):  # Minimum USN record size
                    break

                # Parse USN record header
                record_length = struct.unpack('<I', usn_data[offset:offset+4])[0]
                if record_length == 0 or offset + record_length > len(usn_data):
                    break

                # Extract record data
                major_version = struct.unpack('<H', usn_data[offset+4:offset+6])[0]
                minor_version = struct.unpack('<H', usn_data[offset+6:offset+8])[0]

                if major_version == 2:  # USN_RECORD_V2
                    # Parse USN_RECORD_V2 structure
                    file_reference_number = struct.unpack('<Q', usn_data[offset+8:offset+16])[0]
                    parent_file_reference_number = struct.unpack('<Q', usn_data[offset+16:offset+24])[0]
                    usn = struct.unpack('<Q', usn_data[offset+24:offset+32])[0]
                    timestamp = struct.unpack('<Q', usn_data[offset+32:offset+40])[0]
                    reason = struct.unpack('<I', usn_data[offset+40:offset+44])[0]
                    source_info = struct.unpack('<I', usn_data[offset+44:offset+48])[0]
                    security_id = struct.unpack('<I', usn_data[offset+48:offset+52])[0]
                    file_attributes = struct.unpack('<I', usn_data[offset+52:offset+56])[0]
                    file_name_length = struct.unpack('<H', usn_data[offset+56:offset+58])[0]
                    file_name_offset = struct.unpack('<H', usn_data[offset+58:offset+60])[0]

                    # Check if this is a deletion event
                    USN_REASON_FILE_DELETE = 0x00000200
                    USN_REASON_HARD_LINK_CHANGE = 0x00010000

                    if reason & (USN_REASON_FILE_DELETE | USN_REASON_HARD_LINK_CHANGE):
                        # Convert Windows timestamp to datetime
                        # Windows FILETIME is 100-nanosecond intervals since January 1, 1601
                        dt = datetime(1601, 1, 1) + timedelta(microseconds=timestamp/10)

                        if start_datetime <= dt <= end_datetime:
                            # Extract filename
                            if file_name_length > 0 and offset + file_name_offset + file_name_length <= len(usn_data):
                                filename_bytes = usn_data[offset + file_name_offset:offset + file_name_offset + file_name_length]
                                filename = filename_bytes.decode('utf-16le', errors='ignore')

                                # Determine if it's a file or folder
                                is_directory = bool(file_attributes & 0x10)  # FILE_ATTRIBUTE_DIRECTORY

                                deletions.append({
                                    'datetime': dt,
                                    'user': 'Unknown',  # USN records don't contain user info
                                    'type': 'Folder' if is_directory else 'File',
                                    'path': f'{drive_letter}:\\',
                                    'filename': filename
                                })

                offset += record_length

        except Exception as e:
            print(f"Error parsing USN records: {e}")

        return deletions

    def check_recent_file_operations(self, drive_letter, start_datetime, end_datetime):
        """Fallback method to check for recent file operations"""
        deletions = []

        # This is a placeholder for when USN Journal is not available
        # In a real implementation, you might:
        # 1. Check Windows Event Logs for file system events
        # 2. Compare current directory listings with cached versions
        # 3. Use other forensic techniques

        self.root.after(0, lambda: messagebox.showinfo(
            "Limited Data",
            f"USN Journal parsing failed for drive {drive_letter}.\n\n"
            "This could be due to:\n"
            "• Insufficient permissions (try running as Administrator)\n"
            "• Drive doesn't support USN Journal\n"
            "• Journal data is not available\n\n"
            "Consider enabling audit policies on the file server for better tracking."
        ))

        return deletions

    def display_results(self, deletions):
        """Display analysis results in the GUI"""
        # Stop progress and re-enable button
        self.progress.stop()
        self.analyze_btn.config(state="normal")

        # Clear previous results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # Store results
        self.deletion_records = deletions

        # Populate treeview
        for deletion in deletions:
            self.results_tree.insert("", "end", values=(
                deletion['datetime'].strftime("%Y-%m-%d %H:%M:%S"),
                deletion['user'],
                deletion['type'],
                deletion['path'],
                deletion['filename']
            ))

        # Update status and enable export
        self.status_var.set(f"Found {len(deletions)} deletion records")
        self.export_btn.config(state="normal" if deletions else "disabled")

    def analysis_error(self, error_message):
        """Handle analysis errors"""
        self.progress.stop()
        self.analyze_btn.config(state="normal")
        self.status_var.set("Analysis failed")
        messagebox.showerror("Analysis Error", f"Failed to analyze deletions:\n{error_message}")

    def export_results(self):
        """Export results to CSV file"""
        if not self.deletion_records:
            messagebox.showwarning("Warning", "No results to export")
            return

        # Get save location
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            title="Save deletion report"
        )

        if filename:
            try:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # Write header
                    writer.writerow(["Date/Time", "User", "Type", "Path", "File/Folder Name"])

                    # Write data
                    for deletion in self.deletion_records:
                        writer.writerow([
                            deletion['datetime'].strftime("%Y-%m-%d %H:%M:%S"),
                            deletion['user'],
                            deletion['type'],
                            deletion['path'],
                            deletion['filename']
                        ])

                messagebox.showinfo("Success", f"Results exported to {filename}")
                self.status_var.set(f"Exported {len(self.deletion_records)} records to {filename}")

            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export results:\n{e}")

    def clear_results(self):
        """Clear all results"""
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        self.deletion_records = []
        self.export_btn.config(state="disabled")
        self.status_var.set("Results cleared")

    def run(self):
        """Start the application"""
        self.root.mainloop()

def main():
    """Main entry point"""
    # Check if running as administrator
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
    except:
        is_admin = False

    if not is_admin:
        messagebox.showwarning("Administrator Required",
                             "This application requires administrator privileges to access NTFS Journal data.\n"
                             "Please run as administrator.")

    # Create and run application
    app = FileDeleteionTracker()
    app.run()

if __name__ == "__main__":
    main()
