@echo off
echo Setting up File Deletion Tracker...
echo.

REM Activate virtual environment
if exist ".venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call .venv\Scripts\activate.bat
) else (
    echo Virtual environment not found. Creating one...
    python -m venv .venv
    call .venv\Scripts\activate.bat
)

echo.
echo Installing required packages...
pip install -r requirements.txt

echo.
echo Setup complete! You can now run the application with:
echo python file_deletion_tracker.py
echo.
echo Note: Make sure to run as Administrator for full functionality.
pause
